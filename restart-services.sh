#!/bin/bash

echo "Restarting Docker services to apply new configuration..."

# Stop all services
echo "Stopping services..."
docker-compose down

# Start services in the correct order
echo "Starting PostgreSQL..."
docker-compose up -d postgres_db_docker

echo "Waiting for PostgreSQL to be ready..."
sleep 10

echo "Starting Redis..."
docker-compose up -d redis_cache_docker

echo "Starting MinIO..."
docker-compose up -d minio_s3_docker

echo "Waiting for MinIO to be ready..."
sleep 15

echo "Starting Directus..."
docker-compose up -d directus_backend_docker

echo "All services started. Checking status..."
docker-compose ps

echo ""
echo "Services should be available at:"
echo "- Directus: http://localhost:10055"
echo "- <PERSON><PERSON> Console: http://localhost:10111"
echo "- PostgreSQL: localhost:10432"
echo "- Redis: localhost:10379"
echo ""
echo "If you still get hostname errors, check the logs:"
echo "docker-compose logs directus_backend_docker"
echo "docker-compose logs minio_s3_docker"
