#!/bin/bash

echo "=== MinIO Setup and Troubleshooting ==="

# Load environment variables
if [ -f .env ]; then
    export $(cat .env | grep -v '^#' | xargs)
else
    echo "Error: .env file not found"
    exit 1
fi

echo "MinIO Configuration:"
echo "- Root User: $MINIO_ROOT_USER"
echo "- Root Password: $MINIO_ROOT_PASSWORD"
echo "- Bucket Name: $MINIO_BUCKET_NAME"
echo "- API Port: $MINIO_API_PORT"
echo "- Console Port: $MINIO_CONSOLE_PORT"
echo ""

# Check if MinIO container is running
echo "Checking MinIO container status..."
if docker-compose ps minio_s3_docker | grep -q "Up"; then
    echo "✓ MinIO container is running"
else
    echo "✗ MinIO container is not running. Starting it..."
    docker-compose up -d minio_s3_docker
    echo "Waiting for MinIO to start..."
    sleep 15
fi

# Test MinIO API connectivity
echo ""
echo "Testing MinIO API connectivity..."
if curl -s --max-time 5 http://localhost:$MINIO_API_PORT/minio/health/live > /dev/null; then
    echo "✓ MinIO API is accessible"
else
    echo "✗ MinIO API is not accessible"
    echo "Check if port $MINIO_API_PORT is available"
    exit 1
fi

# Test MinIO Console connectivity
echo ""
echo "Testing MinIO Console connectivity..."
if curl -s --max-time 5 http://localhost:$MINIO_CONSOLE_PORT > /dev/null; then
    echo "✓ MinIO Console is accessible"
else
    echo "✗ MinIO Console is not accessible"
    echo "Check if port $MINIO_CONSOLE_PORT is available"
fi

# Install MinIO client if not available
echo ""
echo "Checking MinIO client..."
if ! command -v mc &> /dev/null; then
    echo "Installing MinIO client..."
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        curl https://dl.min.io/client/mc/release/darwin-amd64/mc -o /tmp/mc
        chmod +x /tmp/mc
        sudo mv /tmp/mc /usr/local/bin/mc
    else
        # Linux
        curl https://dl.min.io/client/mc/release/linux-amd64/mc -o /tmp/mc
        chmod +x /tmp/mc
        sudo mv /tmp/mc /usr/local/bin/mc
    fi
fi

# Configure MinIO client
echo ""
echo "Configuring MinIO client..."
mc alias set local http://localhost:$MINIO_API_PORT $MINIO_ROOT_USER $MINIO_ROOT_PASSWORD

# Test connection
echo ""
echo "Testing MinIO connection..."
if mc admin info local > /dev/null 2>&1; then
    echo "✓ MinIO connection successful"
else
    echo "✗ MinIO connection failed"
    echo "Please check your credentials and try again"
    exit 1
fi

# List existing buckets
echo ""
echo "Existing buckets:"
mc ls local

# Create bucket if it doesn't exist
echo ""
echo "Creating bucket '$MINIO_BUCKET_NAME' if it doesn't exist..."
mc mb local/$MINIO_BUCKET_NAME --ignore-existing

# Set bucket policy for public read
echo ""
echo "Setting bucket policy for public read access..."
cat > /tmp/bucket-policy.json << EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Principal": {
                "AWS": ["*"]
            },
            "Action": ["s3:GetObject"],
            "Resource": ["arn:aws:s3:::$MINIO_BUCKET_NAME/*"]
        }
    ]
}
EOF

mc policy set-json /tmp/bucket-policy.json local/$MINIO_BUCKET_NAME

echo ""
echo "=== Setup Complete ==="
echo "MinIO Console: http://localhost:$MINIO_CONSOLE_PORT"
echo "Login with:"
echo "  Username: $MINIO_ROOT_USER"
echo "  Password: $MINIO_ROOT_PASSWORD"
echo ""
echo "Bucket '$MINIO_BUCKET_NAME' is ready for Directus file uploads."

# Clean up
rm -f /tmp/bucket-policy.json
