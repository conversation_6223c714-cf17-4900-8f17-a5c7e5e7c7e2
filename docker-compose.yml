services:
  directus_backend_docker:
    image: ${DIRECTUS_IMAGE}
    restart: always
    ports:
      - ${DIRECTUS_PORT}:8055
    volumes:
      - directus-data:/data
      - ./extensions:/directus/extensions
    environment:
      SECRET: "${SECRET}"
      ADMIN_EMAIL: "${ADMIN_EMAIL}"
      ADMIN_PASSWORD: "${ADMIN_PASSWORD}"
      WEBSOCKETS_ENABLED: "${WEBSOCKETS_ENABLED}"
      DB_CLIENT: "${DB_CLIENT}"
      CORS_ENABLED: "${CORS_ENABLED}"
      CORS_ORIGIN: "${CORS_ORIGIN}"
      CORS_METHODS: "${CORS_METHODS}"
      CORS_HEADERS: "${CORS_HEADERS}"
      CORS_CREDENTIALS: "${CORS_CREDENTIALS}"

      DB_HOST: "${DB_HOST}"
      DB_PORT: "${POSTGRES_PORT}"
      DB_DATABASE: "${DB_DATABASE}"
      DB_USER: "${DB_USER}"
      DB_PASSWORD: "${DB_PASSWORD}"

      REDIS_ENABLED: "${REDIS_ENABLED}"
      REDIS_HOST: "${REDIS_HOST}"
      REDIS_PORT: "${REDIS_PORT}"
      REDIS_PASS: "${REDIS_PASS}"
      EXTENSIONS_AUTO_RELOAD: "${EXTENSIONS_AUTO_RELOAD}"

      STORAGE_LOCATIONS: "${STORAGE_LOCATIONS}"
      STORAGE_S3_DRIVER: "${STORAGE_S3_DRIVER}"
      STORAGE_S3_KEY: "${STORAGE_S3_KEY}"
      STORAGE_S3_SECRET: "${STORAGE_S3_SECRET}"
      STORAGE_S3_ENDPOINT: "${STORAGE_S3_ENDPOINT}"
      STORAGE_S3_BUCKET: "${STORAGE_S3_BUCKET}"
      STORAGE_S3_REGION: "${STORAGE_S3_REGION}"
      STORAGE_S3_FORCE_PATH_STYLE: "${STORAGE_S3_FORCE_PATH_STYLE}"
      STORAGE_S3_ACL: "${STORAGE_S3_ACL}"


  redis_cache_docker:
    image: ${REDIS_IMAGE}
    restart: always
    ports:
      - '${REDIS_PUBLIC_PORT}:${REDIS_PORT}'
    command: redis-server --save 20 1 --loglevel warning
    volumes: 
      - cache:/data
    environment:
      - REDIS_PASSWORD=${REDIS_PASS}
      - REDIS_PORT=${REDIS_PORT}
      - REDIS_DATABASES=${REDIS_DATABASES}
    deploy:
      resources:
        limits:
          memory: ${REDIS_MEMORY_LIMIT}
        reservations:
          memory: ${REDIS_MEMORY_RESERVATION}

  postgres_db_docker:
    image: ${POSTGRES_IMAGE}
    restart: unless-stopped
    environment:
      POSTGRES_USER: ${DB_USER}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_DB: ${DB_DATABASE}
    volumes:
      - ./docker/postgres1/data:/var/lib/postgresql/data
    ports:
      - ${POSTGRES_PUBLIC_PORT}:${POSTGRES_PORT}
    deploy:
      resources:
        limits:
          memory: ${POSTGRES_MEMORY_LIMIT}
        reservations:
          memory: ${POSTGRES_MEMORY_RESERVATION}

  minio_s3_docker:
    image: ${MINIO_IMAGE}
    restart: unless-stopped
    environment:
      - MINIO_ROOT_USER=${MINIO_ROOT_USER}
      - MINIO_ROOT_PASSWORD=${MINIO_ROOT_PASSWORD}
      - MINIO_SERVER_URL=${MINIO_SERVER_URL}
      - MINIO_BROWSER_REDIRECT_URL=${MINIO_BROWSER_REDIRECT_URL}
    command: server --console-address ":9001" /data
    ports:
      - "${MINIO_API_PORT}:9000"
      - "${MINIO_CONSOLE_PORT}:9001"
    volumes:
      - minio-data:/data
    healthcheck:
      test: ["CMD", "mc", "ready", "local"]
      interval: 30s
      timeout: 20s
      retries: 3

volumes:
  minio-data:
  cache:
    driver: local
  directus-data: