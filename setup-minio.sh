#!/bin/bash

# MinIO Setup Script for Directus
# This script helps create the MinIO bucket and configure it properly

set -e

# Load environment variables
if [ -f .env ]; then
    export $(cat .env | grep -v '^#' | xargs)
else
    echo "Error: .env file not found. Please copy .env.example to .env and configure it."
    exit 1
fi

echo "Setting up MinIO for Directus..."

# Check if MinIO container is running
if ! docker-compose ps minio_s3_docker | grep -q "Up"; then
    echo "Starting MinIO container..."
    docker-compose up -d minio_s3_docker
    echo "Waiting for MinIO to be ready..."
    sleep 10
fi

# Install MinIO client if not available
if ! command -v mc &> /dev/null; then
    echo "Installing MinIO client..."
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        if command -v brew &> /dev/null; then
            brew install minio/stable/mc
        else
            curl https://dl.min.io/client/mc/release/darwin-amd64/mc -o /usr/local/bin/mc
            chmod +x /usr/local/bin/mc
        fi
    else
        # Linux
        curl https://dl.min.io/client/mc/release/linux-amd64/mc -o /usr/local/bin/mc
        chmod +x /usr/local/bin/mc
    fi
fi

# Configure MinIO client
echo "Configuring MinIO client..."
mc alias set local http://localhost:${MINIO_API_PORT} ${MINIO_ROOT_USER} ${MINIO_ROOT_PASSWORD}

# Create bucket if it doesn't exist
echo "Creating bucket: ${MINIO_BUCKET_NAME}"
mc mb local/${MINIO_BUCKET_NAME} --ignore-existing

# Set bucket policy to public read (optional, for public file access)
echo "Setting bucket policy..."
cat > /tmp/bucket-policy.json << EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Principal": {
                "AWS": ["*"]
            },
            "Action": ["s3:GetObject"],
            "Resource": ["arn:aws:s3:::${MINIO_BUCKET_NAME}/*"]
        }
    ]
}
EOF

mc policy set-json /tmp/bucket-policy.json local/${MINIO_BUCKET_NAME}

echo "MinIO setup completed successfully!"
echo "Bucket '${MINIO_BUCKET_NAME}' is ready for use."
echo "MinIO Console: http://localhost:${MINIO_CONSOLE_PORT}"
echo "API Endpoint: http://localhost:${MINIO_API_PORT}"

# Clean up
rm -f /tmp/bucket-policy.json
