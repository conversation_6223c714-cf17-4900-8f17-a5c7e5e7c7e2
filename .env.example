# Directus Admin
SECRET=2esZ6iakBGoHPFOIlv0TOT5kcIx9qq
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=BX56rZELNiWzsFw

# Database (Postgres)
DB_CLIENT=pg
DB_HOST=postgres_db_docker
# DB_PORT=5432
DB_DATABASE=directus
DB_USER=directus
DB_PASSWORD=C9R2lfydrn7Ka5f

# Redis
REDIS_ENABLED=true
REDIS_HOST=redis_cache_docker
REDIS_PORT=6379
REDIS_PUBLIC_PORT=10379
REDIS_PASS=your_redis_password
REDIS_IMAGE="redis:7.4.5-alpine"
REDIS_DATABASES=16
REDIS_MEMORY_LIMIT=256M
REDIS_MEMORY_RESERVATION=128M

# CORS
CORS_ENABLED=true
CORS_ORIGIN=*
CORS_METHODS=GET,POST,PATCH,DELETE,OPTIONS
CORS_HEADERS=Content-Type,Authorization
CORS_CREDENTIALS=true

# Directus Extensions
EXTENSIONS_AUTO_RELOAD=true

# Directus
DIRECTUS_IMAGE="directus/directus:11.5.1"
DIRECTUS_PORT=10055
WEBSOCKETS_ENABLED=true

# Postgres Service (minicrmbe_db1)
POSTGRES_IMAGE="postgres:16.0-alpine"
POSTGRES_PORT=5432
POSTGRES_PUBLIC_PORT=10432
POSTGRES_MEMORY_LIMIT=256M
POSTGRES_MEMORY_RESERVATION=128M

# MinIO S3
MINIO_IMAGE="minio/minio:RELEASE.2025-04-22T22-12-26Z"
MINIO_ROOT_USER=admin
MINIO_ROOT_PASSWORD=raEWwtt4DhQt0ZVA
# MINIO_ACCESS_KEY=MHLN2kQ2250Uk6SB
# MINIO_SECRET_KEY=DTfS7b5Jr1zFkWot
MINIO_REGION=ap-southeast-1
MINIO_BUCKET_NAME=cellphones_directus

# MinIO server settings
#Should be "http://minio:9000" for internal Docker access
MINIO_ENDPOINT=http://minio_s3_docker:9000
MINIO_SERVER_URL=http://localhost:10110
MINIO_BROWSER_REDIRECT_URL=http://localhost:10111
# MINIO_VOLUMES=/data

# MinIO ports (public ports)
MINIO_API_PORT=10110
MINIO_CONSOLE_PORT=10111

#AWS S3 MINIO
# AWS_ACCESS_KEY_ID=${MINIO_ROOT_USER}
# AWS_ACCESS_SECRET=${MINIO_ROOT_PASSWORD}
# AWS_ENDPOINT=${MINIO_ENDPOINT}
# AWS_FILE_URL=https://cpss3.appmkt.vn/cellphones
# AWS_REGION=${MINIO_REGION}
# AWS_BUCKET=${MINIO_BUCKET_NAME}
# # Required for MinIO
# AWS_S3_FORCE_PATH_STYLE=true


STORAGE_LOCATIONS=s3
STORAGE_S3_DRIVER=s3
STORAGE_S3_KEY=${MINIO_ROOT_USER}
STORAGE_S3_SECRET=${MINIO_ROOT_PASSWORD}
STORAGE_S3_ENDPOINT=${MINIO_ENDPOINT}
STORAGE_S3_BUCKET=${MINIO_BUCKET_NAME}
STORAGE_S3_REGION=${MINIO_REGION}
# required for MinIO path-style
STORAGE_S3_FORCE_PATH_STYLE=true      
# Optional: make objects public at upload time (you must also allow bucket read)
STORAGE_S3_ACL="public-read"
