# =============================================================================
# DIRECTUS CONFIGURATION
# =============================================================================
# Copy this file to .env and update the values according to your environment
# For production, use .env.prod as reference

# Directus Admin
SECRET=replace_with_your_secret_key_here
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=replace_with_secure_password

# Database (Postgres)
DB_CLIENT=pg
DB_HOST=postgres_db_docker
# DB_PORT=5432
DB_DATABASE=directus
DB_USER=directus
DB_PASSWORD=replace_with_secure_db_password

# Redis
REDIS_ENABLED=true
REDIS_HOST=redis_cache_docker
REDIS_PORT=6379
REDIS_PUBLIC_PORT=10379
REDIS_PASS=replace_with_secure_redis_password
REDIS_IMAGE="redis:7.4.5-alpine"
REDIS_DATABASES=16
REDIS_MEMORY_LIMIT=256M
REDIS_MEMORY_RESERVATION=128M

# CORS
CORS_ENABLED=true
CORS_ORIGIN=*
CORS_METHODS=GET,POST,PATCH,DELETE,OPTIONS
CORS_HEADERS=Content-Type,Authorization
CORS_CREDENTIALS=true

# Directus Extensions
EXTENSIONS_AUTO_RELOAD=true

# Directus
DIRECTUS_IMAGE="directus/directus:11.5.1"
DIRECTUS_PORT=10055
WEBSOCKETS_ENABLED=true

# Postgres Service (minicrmbe_db1)
POSTGRES_IMAGE="postgres:16.0-alpine"
POSTGRES_PORT=5432
POSTGRES_PUBLIC_PORT=10432
POSTGRES_MEMORY_LIMIT=256M
POSTGRES_MEMORY_RESERVATION=128M

# MinIO S3 Configuration
MINIO_IMAGE="minio/minio:RELEASE.2025-04-22T22-12-26Z"
MINIO_ROOT_USER=admin
MINIO_ROOT_PASSWORD=replace_with_secure_minio_password
MINIO_REGION=ap-southeast-1
MINIO_BUCKET_NAME=your_bucket_name

# MinIO server settings
# Internal Docker network endpoint (used by Directus container)
MINIO_ENDPOINT=http://minio_s3_docker:9000
# External URLs for browser access
MINIO_SERVER_URL=http://localhost:10110
MINIO_BROWSER_REDIRECT_URL=http://localhost:10111
# MINIO_VOLUMES=/data

# MinIO ports (public ports)
MINIO_API_PORT=10110
MINIO_CONSOLE_PORT=10111

# S3 Storage Configuration for Directus
STORAGE_LOCATIONS=s3
STORAGE_S3_DRIVER=s3
STORAGE_S3_KEY=${MINIO_ROOT_USER}
STORAGE_S3_SECRET=${MINIO_ROOT_PASSWORD}
STORAGE_S3_ENDPOINT=${MINIO_ENDPOINT}
STORAGE_S3_BUCKET=${MINIO_BUCKET_NAME}
STORAGE_S3_REGION=${MINIO_REGION}
# Required for MinIO path-style URLs
STORAGE_S3_FORCE_PATH_STYLE=true
# Optional: make objects public at upload time (you must also allow bucket read)
STORAGE_S3_ACL="public-read"

# Additional S3 Configuration (uncomment if needed)
# AWS_ACCESS_KEY_ID=${MINIO_ROOT_USER}
# AWS_SECRET_ACCESS_KEY=${MINIO_ROOT_PASSWORD}
# AWS_ENDPOINT=${MINIO_ENDPOINT}
# AWS_REGION=${MINIO_REGION}
# AWS_BUCKET=${MINIO_BUCKET_NAME}
# AWS_S3_FORCE_PATH_STYLE=true

# =============================================================================
# TROUBLESHOOTING NOTES
# =============================================================================
#
# If you get "Invalid Request (invalid hostname)" errors:
# 1. Ensure all Docker services are running: docker-compose up -d
# 2. Check MinIO is accessible: docker-compose logs minio_s3_docker
# 3. Verify the bucket exists in MinIO console: http://localhost:10111
# 4. Make sure MINIO_ENDPOINT uses the Docker service name: minio_s3_docker
# 5. Ensure STORAGE_S3_FORCE_PATH_STYLE=true for MinIO compatibility
#
# To create the bucket manually:
# 1. Access MinIO console at http://localhost:10111
# 2. Login with MINIO_ROOT_USER and MINIO_ROOT_PASSWORD
# 3. Create a bucket with the name specified in MINIO_BUCKET_NAME
# 4. Set bucket policy to allow public read if needed
